'use client';

import { useRef, useEffect, useState, useCallback } from 'react';
import { useThree } from '@react-three/fiber';
import * as THREE from 'three';

/**
 * AmbientAudio Component
 *
 * A React Three.js Fiber component that plays ambient background music
 * using the Ambient_Music.mp4 file from the public assets folder.
 *
 * Features:
 * - Automatic playback on mount
 * - Continuous looping
 * - Configurable volume
 * - Error handling for loading failures
 * - Performance optimized with proper cleanup
 *
 * Usage:
 * <Canvas>
 *   <AmbientAudio volume={0.4} autoPlay={true} />
 * </Canvas>
 */
function AmbientAudio({
  volume = 0.4,
  autoPlay = true,
  loop = true,
  onLoad = null,
  onError = null
}) {
  const audioRef = useRef();
  const listenerRef = useRef();
  const [isLoaded, setIsLoaded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState(null);
  const { camera } = useThree();

  // Audio file path
  const audioUrl = '/assets/Ambient_Music.mp4';

  // Load audio manually with better error handling
  useEffect(() => {
    let isMounted = true;

    const loadAudio = async () => {
      try {
        // Create audio listener if it doesn't exist
        if (!listenerRef.current) {
          listenerRef.current = new THREE.AudioListener();
          camera.add(listenerRef.current);
        }

        // Create audio object
        const audio = new THREE.Audio(listenerRef.current);
        audioRef.current = audio;

        // Load audio using HTML5 Audio element (more reliable for MP4)
        const audioElement = new Audio(audioUrl);
        audioElement.crossOrigin = 'anonymous';
        audioElement.loop = loop;
        audioElement.volume = volume;

        // Wait for audio to be ready
        await new Promise((resolve, reject) => {
          const handleCanPlay = () => {
            audioElement.removeEventListener('canplaythrough', handleCanPlay);
            audioElement.removeEventListener('error', handleError);
            resolve();
          };

          const handleError = (err) => {
            audioElement.removeEventListener('canplaythrough', handleCanPlay);
            audioElement.removeEventListener('error', handleError);
            reject(err);
          };

          audioElement.addEventListener('canplaythrough', handleCanPlay);
          audioElement.addEventListener('error', handleError);

          // Start loading
          audioElement.load();
        });

        // Set the media element source for Three.js Audio
        audio.setMediaElementSource(audioElement);

        if (isMounted) {
          setIsLoaded(true);
          setError(null);
          console.log('Ambient audio loaded successfully');
          if (onLoad) onLoad();
        }

      } catch (err) {
        console.error('Failed to load ambient audio:', err);
        if (isMounted) {
          setError(err);
          setIsLoaded(false);
          if (onError) onError(err);
        }
      }
    };

    loadAudio();

    return () => {
      isMounted = false;
      // Cleanup
      if (audioRef.current) {
        if (audioRef.current.isPlaying) {
          audioRef.current.stop();
        }
        audioRef.current = null;
      }
      if (listenerRef.current && camera) {
        camera.remove(listenerRef.current);
        listenerRef.current = null;
      }
    };
  }, [audioUrl, camera, loop, volume, onLoad, onError]);

  // Handle autoplay and audio control
  useEffect(() => {
    if (!audioRef.current || !isLoaded) return;

    const audio = audioRef.current;

    // Auto-play if enabled
    if (autoPlay) {
      const playAudio = async () => {
        try {
          if (!audio.isPlaying) {
            await audio.play();
            setIsPlaying(true);
            console.log('Ambient audio started playing');
          }
        } catch (playError) {
          console.warn('Autoplay prevented by browser policy:', playError);

          // Set up a user interaction listener to start audio
          const startOnInteraction = () => {
            if (!audio.isPlaying) {
              audio.play().then(() => {
                setIsPlaying(true);
                console.log('Ambient audio started after user interaction');
              }).catch(console.error);
            }
            // Remove listeners after first interaction
            document.removeEventListener('click', startOnInteraction);
            document.removeEventListener('keydown', startOnInteraction);
            document.removeEventListener('touchstart', startOnInteraction);
          };

          document.addEventListener('click', startOnInteraction);
          document.addEventListener('keydown', startOnInteraction);
          document.addEventListener('touchstart', startOnInteraction);
        }
      };

      playAudio();
    }
  }, [isLoaded, autoPlay]);

  // Update volume when prop changes
  useEffect(() => {
    if (audioRef.current && isLoaded) {
      audioRef.current.setVolume(volume);
    }
  }, [volume, isLoaded]);

  // Public methods for controlling playback
  const play = useCallback(() => {
    if (audioRef.current && isLoaded && !audioRef.current.isPlaying) {
      audioRef.current.play().then(() => {
        setIsPlaying(true);
      }).catch(console.error);
    }
  }, [isLoaded]);

  const pause = useCallback(() => {
    if (audioRef.current && audioRef.current.isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  }, []);

  const stop = useCallback(() => {
    if (audioRef.current && audioRef.current.isPlaying) {
      audioRef.current.stop();
      setIsPlaying(false);
    }
  }, []);

  // Expose control methods for external use (optional)
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.customControls = {
        play,
        pause,
        stop,
        isPlaying,
        isLoaded,
        error
      };
    }
  }, [play, pause, stop, isPlaying, isLoaded, error]);

  // Don't render anything - this is a non-visual audio component
  // The audio is managed through Three.js Audio objects in useEffect
  return null;
}

export default AmbientAudio;
