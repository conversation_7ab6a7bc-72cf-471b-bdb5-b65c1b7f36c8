'use client';

import { useRef, useEffect, useState, useCallback } from 'react';
import { useLoader, useThree } from '@react-three/fiber';
import { PositionalAudio } from '@react-three/drei';
import * as THREE from 'three';

/**
 * AmbientAudio Component
 * 
 * A React Three.js Fiber component that plays ambient background music
 * using the Ambient_Music.mp4 file from the public assets folder.
 * 
 * Features:
 * - Automatic playback on mount
 * - Continuous looping
 * - Configurable volume
 * - Error handling for loading failures
 * - Performance optimized with proper cleanup
 * 
 * Usage:
 * <Canvas>
 *   <AmbientAudio volume={0.4} autoPlay={true} />
 * </Canvas>
 */
function AmbientAudio({ 
  volume = 0.4, 
  autoPlay = true, 
  loop = true,
  position = [0, 0, 0],
  distance = 10,
  onLoad = null,
  onError = null 
}) {
  const audioRef = useRef();
  const [isLoaded, setIsLoaded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState(null);
  const { camera, scene } = useThree();

  // Audio file path
  const audioUrl = '/assets/Ambient_Music.mp4';

  // Load audio using Three.js AudioLoader
  const audioBuffer = useLoader(
    THREE.AudioLoader,
    audioUrl,
    // onLoad callback
    useCallback(() => {
      console.log('Ambient audio loaded successfully');
      setIsLoaded(true);
      setError(null);
      if (onLoad) onLoad();
    }, [onLoad]),
    // onError callback
    useCallback((err) => {
      console.error('Failed to load ambient audio:', err);
      setError(err);
      setIsLoaded(false);
      if (onError) onError(err);
    }, [onError])
  );

  // Initialize and configure audio
  useEffect(() => {
    if (!audioRef.current || !audioBuffer || !isLoaded) return;

    const audio = audioRef.current;
    
    try {
      // Set audio buffer
      audio.setBuffer(audioBuffer);
      
      // Configure audio properties
      audio.setLoop(loop);
      audio.setVolume(volume);
      
      // For ambient audio, we typically want it to be non-positional
      // but we'll use PositionalAudio for better Three.js integration
      audio.setRefDistance(distance);
      audio.setRolloffFactor(0.1); // Minimal rolloff for ambient sound
      audio.setDistanceModel('linear');
      
      console.log('Ambient audio configured successfully');
      
      // Auto-play if enabled and user has interacted with the page
      if (autoPlay) {
        // We need to handle the browser's autoplay policy
        const playAudio = async () => {
          try {
            if (!audio.isPlaying) {
              await audio.play();
              setIsPlaying(true);
              console.log('Ambient audio started playing');
            }
          } catch (playError) {
            console.warn('Autoplay prevented by browser policy:', playError);
            // Set up a user interaction listener to start audio
            const startOnInteraction = () => {
              if (!audio.isPlaying) {
                audio.play().then(() => {
                  setIsPlaying(true);
                  console.log('Ambient audio started after user interaction');
                }).catch(console.error);
              }
              // Remove listeners after first interaction
              document.removeEventListener('click', startOnInteraction);
              document.removeEventListener('keydown', startOnInteraction);
              document.removeEventListener('touchstart', startOnInteraction);
            };
            
            document.addEventListener('click', startOnInteraction);
            document.addEventListener('keydown', startOnInteraction);
            document.addEventListener('touchstart', startOnInteraction);
          }
        };
        
        playAudio();
      }
      
    } catch (configError) {
      console.error('Error configuring ambient audio:', configError);
      setError(configError);
    }
    
    // Cleanup function
    return () => {
      if (audio && audio.isPlaying) {
        audio.stop();
        setIsPlaying(false);
      }
    };
  }, [audioBuffer, isLoaded, volume, loop, autoPlay, distance]);

  // Update volume when prop changes
  useEffect(() => {
    if (audioRef.current && isLoaded) {
      audioRef.current.setVolume(volume);
    }
  }, [volume, isLoaded]);

  // Public methods for controlling playback
  const play = useCallback(() => {
    if (audioRef.current && isLoaded && !audioRef.current.isPlaying) {
      audioRef.current.play().then(() => {
        setIsPlaying(true);
      }).catch(console.error);
    }
  }, [isLoaded]);

  const pause = useCallback(() => {
    if (audioRef.current && audioRef.current.isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  }, []);

  const stop = useCallback(() => {
    if (audioRef.current && audioRef.current.isPlaying) {
      audioRef.current.stop();
      setIsPlaying(false);
    }
  }, []);

  // Expose control methods via ref (for parent components)
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.customControls = {
        play,
        pause,
        stop,
        isPlaying,
        isLoaded,
        error
      };
    }
  }, [play, pause, stop, isPlaying, isLoaded, error]);

  // Don't render if there's an error
  if (error) {
    console.error('AmbientAudio component error:', error);
    return null;
  }

  // Render the PositionalAudio component
  return (
    <PositionalAudio
      ref={audioRef}
      position={position}
      args={[camera]} // Pass camera as argument for PositionalAudio
    />
  );
}

export default AmbientAudio;
